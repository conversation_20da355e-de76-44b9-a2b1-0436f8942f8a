{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Vendor Management{% endblock %}
{% block page_title %}Vendor Management{% endblock %}
{% block page_description %}Manage vendor applications and approvals{% endblock %}

{% comment %} {% block sidebar_nav %}
    <li>
        <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_users' %}" class="dashboard-nav-link">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_vendors' %}" class="dashboard-nav-link active">
            <i class="fas fa-store"></i>
            <span>Vendor Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_listings' %}" class="dashboard-nav-link">
            <i class="fas fa-car"></i>
            <span>Car Listings</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-ship"></i>
            <span>Import Requests</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-tools"></i>
            <span>Spare Parts</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-envelope"></i>
            <span>Inquiries</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-newspaper"></i>
            <span>Content Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_analytics' %}" class="dashboard-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </a>
    </li>
{% endblock %} {% endcomment %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Vendor Management</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    {% csrf_token %}
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-harrier-dark font-montserrat">{{ total_vendors|default:0 }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Total Vendors</div>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-store text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-green-600 font-montserrat">{{ approved_vendors|default:0 }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Approved Vendors</div>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-orange-600 font-montserrat">{{ pending_vendors|default:0 }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Pending Approval</div>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-red-600 font-montserrat">{{ suspended_vendors|default:0 }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Suspended</div>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-ban text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Status Filter -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-harrier-dark">
                        <i class="fas fa-filter mr-1 text-blue-500"></i>Status:
                    </label>
                    <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red text-sm"
                            onchange="window.location.href='?status=' + this.value + (new URLSearchParams(window.location.search).get('search') ? '&search=' + new URLSearchParams(window.location.search).get('search') : '')">
                        <option value="">All Vendors</option>
                        <option value="approved" {% if current_filter == 'approved' %}selected{% endif %}>✅ Approved</option>
                        <option value="pending" {% if current_filter == 'pending' %}selected{% endif %}>⏳ Pending</option>
                        <option value="suspended" {% if current_filter == 'suspended' %}selected{% endif %}>🚫 Suspended</option>
                    </select>
                </div>

                <!-- Business Type Filter -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-harrier-dark">
                        <i class="fas fa-industry mr-1 text-purple-500"></i>Type:
                    </label>
                    <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red text-sm"
                            onchange="window.location.href='?business_type=' + this.value + (new URLSearchParams(window.location.search).get('search') ? '&search=' + new URLSearchParams(window.location.search).get('search') : '') + (new URLSearchParams(window.location.search).get('status') ? '&status=' + new URLSearchParams(window.location.search).get('status') : '')">
                        <option value="">All Types</option>
                        <option value="dealership" {% if request.GET.business_type == 'dealership' %}selected{% endif %}>Car Dealership</option>
                        <option value="spare_parts" {% if request.GET.business_type == 'spare_parts' %}selected{% endif %}>Spare Parts</option>
                        <option value="both" {% if request.GET.business_type == 'both' %}selected{% endif %}>Both</option>
                        <option value="service_center" {% if request.GET.business_type == 'service_center' %}selected{% endif %}>Service Center</option>
                        <option value="individual" {% if request.GET.business_type == 'individual' %}selected{% endif %}>Individual</option>
                    </select>
                </div>
            </div>

            <!-- Search -->
            <div class="flex items-center space-x-2">
                <form method="GET" class="flex">
                    <input type="text" name="search" placeholder="Search vendors..." 
                           value="{{ request.GET.search }}"
                           class="px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-harrier-red focus:border-harrier-red text-sm">
                    <button type="submit" class="bg-harrier-red text-white px-4 py-2 rounded-r-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Vendors Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-heading font-bold text-harrier-dark">Vendor Applications</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Vendor Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact Info
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Applied Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for vendor in vendors %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <div class="h-12 w-12 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold">
                                            {{ vendor.company_name|first|upper }}
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-harrier-dark">{{ vendor.company_name }}</div>
                                        <div class="text-sm text-gray-500">{{ vendor.user.get_full_name|default:vendor.user.username }}</div>
                                        <div class="flex items-center space-x-2 mt-1">
                                            {% if vendor.business_license %}
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-certificate mr-1"></i>Licensed
                                                </span>
                                            {% endif %}
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ vendor.get_business_type_display }}
                                            </span>
                                            {% if vendor.year_established %}
                                                <span class="text-xs text-gray-500">Est. {{ vendor.year_established }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-harrier-dark">{{ vendor.user.email }}</div>
                                {% if vendor.user.phone %}
                                    <div class="text-sm text-gray-500">{{ vendor.user.phone }}</div>
                                {% endif %}
                                {% if vendor.business_phone %}
                                    <div class="text-sm text-blue-600">
                                        <i class="fas fa-building mr-1"></i>{{ vendor.business_phone }}
                                    </div>
                                {% endif %}
                                {% if vendor.physical_address %}
                                    <div class="text-xs text-gray-500">{{ vendor.physical_address|truncatechars:30 }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="space-y-1">
                                    {% if not vendor.user.is_active %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-ban mr-1"></i>Suspended
                                        </span>
                                    {% elif vendor.is_approved %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>Approved
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            <i class="fas fa-clock mr-1"></i>Pending
                                        </span>
                                    {% endif %}

                                    {% if vendor.verification_status %}
                                        <div class="text-xs text-gray-500">
                                            {{ vendor.get_verification_status_display }}
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ vendor.created_at|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    {% if not vendor.is_approved %}
                                        <button onclick="approveVendor({{ vendor.id }})"
                                                class="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-xs font-medium">
                                            <i class="fas fa-check mr-1"></i>Approve
                                        </button>
                                    {% else %}
                                        <button onclick="disapproveVendor({{ vendor.id }})"
                                                class="inline-flex items-center px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-xs font-medium">
                                            <i class="fas fa-times mr-1"></i>Revoke
                                        </button>
                                    {% endif %}

                                    <a href="{% url 'core:admin_user_detail' vendor.user.id %}"
                                       class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-xs font-medium">
                                        <i class="fas fa-user mr-1"></i>User
                                    </a>

                                    <div class="relative inline-block text-left">
                                        <button type="button" onclick="toggleDropdown({{ vendor.id }})"
                                                class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-xs font-medium">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div id="dropdown-{{ vendor.id }}" class="hidden absolute right-0 z-10 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200">
                                            <div class="py-1">
                                                <a href="#" onclick="viewVendorDetails({{ vendor.id }})"
                                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-eye mr-2"></i>View Details
                                                </a>
                                                <a href="#" onclick="editVendor({{ vendor.id }})"
                                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                                </a>
                                                <a href="#" onclick="viewVendorListings({{ vendor.id }})"
                                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-car mr-2"></i>View Listings
                                                </a>
                                                <div class="border-t border-gray-100"></div>
                                                <a href="#" onclick="suspendVendor({{ vendor.id }})"
                                                   class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                    <i class="fas fa-ban mr-2"></i>Suspend
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-store text-4xl mb-4"></i>
                                    <p class="text-lg">No vendors found</p>
                                    <p class="text-sm">No vendor applications match your current filters.</p>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if vendors.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ vendors.start_index }} to {{ vendors.end_index }} of {{ vendors.paginator.count }} vendors
                </div>
                <div class="flex items-center space-x-2">
                    {% if vendors.has_previous %}
                        <a href="?page={{ vendors.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-chevron-left mr-1"></i>Previous
                        </a>
                    {% endif %}

                    {% for num in vendors.paginator.page_range %}
                        {% if num == vendors.number %}
                            <span class="px-3 py-2 text-sm bg-harrier-red text-white rounded-lg">{{ num }}</span>
                        {% elif num > vendors.number|add:'-3' and num < vendors.number|add:'3' %}
                            <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                               class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">{{ num }}</a>
                        {% endif %}
                    {% endfor %}

                    {% if vendors.has_next %}
                        <a href="?page={{ vendors.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            Next<i class="fas fa-chevron-right ml-1"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Export Options -->
    <div class="mt-6 flex justify-end">
        <div class="flex items-center space-x-3">
            <a href="{% url 'core:export_vendors' %}?format=csv{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
               class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-download mr-2"></i>Export CSV
            </a>
            <a href="{% url 'core:export_vendors' %}?format=excel{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-file-excel mr-2"></i>Export Excel
            </a>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// Dropdown functionality
function toggleDropdown(vendorId) {
    const dropdown = document.getElementById(`dropdown-${vendorId}`);
    const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');

    // Close all other dropdowns
    allDropdowns.forEach(d => {
        if (d.id !== `dropdown-${vendorId}`) {
            d.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick*="toggleDropdown"]')) {
        document.querySelectorAll('[id^="dropdown-"]').forEach(d => {
            d.classList.add('hidden');
        });
    }
});

// Vendor approval functions
function approveVendor(vendorId) {
    if (confirm('Are you sure you want to approve this vendor?')) {
        fetch(`/dashboard/admin/approve-vendor/${vendorId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'approved') {
                showToast('Vendor approved successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Failed to approve vendor.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while approving the vendor.', 'error');
        });
    }
}

function disapproveVendor(vendorId) {
    if (confirm('Are you sure you want to revoke approval for this vendor?')) {
        fetch(`/dashboard/admin/disapprove-vendor/${vendorId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'disapproved') {
                showToast('Vendor approval revoked successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Failed to revoke vendor approval.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while revoking vendor approval.', 'error');
        });
    }
}

// Additional vendor actions
function viewVendorDetails(vendorId) {
    // Redirect to vendor detail page or open modal
    window.location.href = `/dashboard/admin/vendors/${vendorId}/`;
}

function editVendor(vendorId) {
    // Redirect to vendor edit page
    window.location.href = `/dashboard/admin/vendors/${vendorId}/edit/`;
}

function viewVendorListings(vendorId) {
    // Redirect to vendor listings
    window.location.href = `/dashboard/admin/listings/?vendor=${vendorId}`;
}

function suspendVendor(vendorId) {
    if (confirm('Are you sure you want to suspend this vendor? This will deactivate their account.')) {
        fetch(`/dashboard/admin/suspend-vendor/${vendorId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'suspended') {
                showToast('Vendor suspended successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Failed to suspend vendor.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while suspending the vendor.', 'error');
        });
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        toast.classList.add('bg-green-500');
    } else if (type === 'error') {
        toast.classList.add('bg-red-500');
    } else {
        toast.classList.add('bg-blue-500');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
